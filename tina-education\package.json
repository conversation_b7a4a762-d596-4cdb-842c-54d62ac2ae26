{"name": "tina-education", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "prebuild": "ts-node scripts/build-search-index.ts", "build": "prisma generate --no-engine && prisma migrate deploy && next build", "start": "next start", "lint": "next lint", "vercel-build": "prisma generate --no-engine && prisma migrate deploy && next build", "seed:repository": "node scripts/seed-repository.js", "seed:publications": "node scripts/seed-publications.js"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@prisma/client": "^6.7.0", "@radix-ui/react-slot": "^1.2.3", "@tiptap/extension-character-count": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@types/bcryptjs": "^2.4.6", "@vercel/blob": "^1.1.1", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "embla-carousel-react": "^8.6.0", "lucide-react": "^0.539.0", "lunr": "^2.3.9", "natural": "^8.1.0", "next": "15.3.1", "next-auth": "^5.0.0-beta.28", "pdf-lib": "^1.17.1", "prisma": "^6.7.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "resend": "^4.5.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/lunr": "^2.3.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "prisma": "^6.7.0", "ts-node": "^10.9.2", "tw-animate-css": "^1.3.6", "typescript": "^5.8.3"}}