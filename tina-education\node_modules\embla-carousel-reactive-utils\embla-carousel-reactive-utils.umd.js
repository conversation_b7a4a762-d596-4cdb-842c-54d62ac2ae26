!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((e="undefined"!=typeof globalThis?globalThis:e||self).EmblaCarouselReactiveUtils={})}(this,(function(e){"use strict";function n(e){return function(e){return"[object Object]"===Object.prototype.toString.call(e)}(e)||Array.isArray(e)}function t(e,o){const i=Object.keys(e),r=Object.keys(o);if(i.length!==r.length)return!1;return JSON.stringify(Object.keys(e.breakpoints||{}))===JSON.stringify(Object.keys(o.breakpoints||{}))&&i.every((i=>{const r=e[i],u=o[i];return"function"==typeof r?`${r}`==`${u}`:n(r)&&n(u)?t(r,u):r===u}))}function o(e){return e.concat().sort(((e,n)=>e.name>n.name?1:-1)).map((e=>e.options))}e.areOptionsEqual=t,e.arePluginsEqual=function(e,n){if(e.length!==n.length)return!1;const i=o(e),r=o(n);return i.every(((e,n)=>t(e,r[n])))},e.canUseDOM=function(){return!("undefined"==typeof window||!window.document||!window.document.createElement)},e.sortAndMapPluginToOptions=o}));
